# -*- coding: utf-8 -*-
"""
删除检验记录相关数据库表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_config import get_db_connection

def drop_inspection_tables():
    """删除检验记录相关的数据库表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🗑️ 开始删除检验记录相关数据库表...")
        
        # 删除抽样检验表
        try:
            cursor.execute("DROP TABLE IF EXISTS sampling_inspection")
            print("✅ 抽样检验表 (sampling_inspection) 删除成功")
        except Exception as e:
            print(f"❌ 删除抽样检验表失败: {e}")
        
        # 删除全部检验表
        try:
            cursor.execute("DROP TABLE IF EXISTS full_inspection")
            print("✅ 全部检验表 (full_inspection) 删除成功")
        except Exception as e:
            print(f"❌ 删除全部检验表失败: {e}")
        
        # 提交更改
        conn.commit()
        print("✅ 所有检验记录表删除完成")
        
    except Exception as e:
        print(f"❌ 删除操作失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("⚠️ 警告：此操作将删除所有检验记录数据，请确认是否继续？")
    confirm = input("输入 'YES' 确认删除: ")
    
    if confirm == 'YES':
        drop_inspection_tables()
        print("🎉 检验记录表删除操作完成")
    else:
        print("❌ 操作已取消")
