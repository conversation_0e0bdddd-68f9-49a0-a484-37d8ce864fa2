from flask import render_template, request, jsonify, flash, redirect, url_for
from . import material_management_bp
from db_config import get_db_connection

@material_management_bp.route('/')
def index():
    """物料管理首页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 获取搜索参数
        search_term = request.args.get('search', '').strip()

        # 获取高级搜索参数
        material_number = request.args.get('material_number', '').strip()
        material_name = request.args.get('material_name', '').strip()
        specification = request.args.get('specification', '').strip()
        material_type = request.args.get('material_type', '').strip()
        color = request.args.get('color', '').strip()
        material_category = request.args.get('material_category', '').strip()
        inspection_type = request.args.get('inspection_type', '').strip()

        # 构建查询语句
        conditions = []
        params = []

        if search_term:
            # 快速搜索
            conditions.append("""
                (material_number LIKE %s
                 OR material_name LIKE %s
                 OR specification LIKE %s
                 OR material_type LIKE %s)
            """)
            search_pattern = f'%{search_term}%'
            params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

        # 高级搜索条件
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f'%{material_number}%')

        if material_name:
            conditions.append("material_name LIKE %s")
            params.append(f'%{material_name}%')

        if specification:
            conditions.append("specification LIKE %s")
            params.append(f'%{specification}%')

        if material_type:
            conditions.append("material_type LIKE %s")
            params.append(f'%{material_type}%')

        if color:
            conditions.append("color LIKE %s")
            params.append(f'%{color}%')

        # 检查字段是否存在
        def check_column_exists(column_name):
            try:
                cursor.execute(f"SHOW COLUMNS FROM materials LIKE '{column_name}'")
                return cursor.fetchone() is not None
            except:
                return False

        # 只有字段存在时才添加搜索条件
        if material_category and check_column_exists('material_category'):
            conditions.append("material_category = %s")
            params.append(material_category)

        if inspection_type and check_column_exists('inspection_type'):
            conditions.append("inspection_type = %s")
            params.append(inspection_type)

        # 构建完整查询
        if conditions:
            query = f"""
                SELECT * FROM materials
                WHERE {' AND '.join(conditions)}
                ORDER BY created_at DESC
                LIMIT 100
            """
            cursor.execute(query, params)
        else:
            # 默认显示最新的20条记录
            cursor.execute("""
                SELECT * FROM materials
                ORDER BY created_at DESC
                LIMIT 20
            """)

        materials = cursor.fetchall()

        # 获取统计信息
        cursor.execute("SELECT COUNT(*) as total FROM materials")
        total_count = cursor.fetchone()['total']

        # 如果有搜索，获取搜索结果数量
        search_count = len(materials) if (search_term or any([material_number, material_name, specification, material_type, color, material_category, inspection_type])) else None

        return render_template('material_management/index.html',
                             materials=materials,
                             total_count=total_count,
                             search_term=search_term,
                             search_count=search_count,
                             # 传递高级搜索参数以便表单回显
                             material_number=material_number,
                             material_name=material_name,
                             specification=specification,
                             material_type=material_type,
                             color=color,
                             material_category=material_category,
                             inspection_type=inspection_type)

    except Exception as e:
        flash(f'获取物料列表失败: {str(e)}', 'error')
        return render_template('material_management/index.html',
                             materials=[],
                             total_count=0,
                             search_term='',
                             search_count=None)
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/new')
def new_material():
    """新增物料页面"""
    return render_template('material_management/new_material.html')

@material_management_bp.route('/edit/<material_id>')
def edit_material(material_id):
    """编辑物料页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM materials WHERE id = %s", (material_id,))
        material = cursor.fetchone()
        
        if not material:
            flash('物料不存在', 'error')
            return redirect(url_for('material_management.index'))
        
        return render_template('material_management/edit_material.html', material=material)
        
    except Exception as e:
        flash(f'获取物料信息失败: {str(e)}', 'error')
        return redirect(url_for('material_management.index'))
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/view/<material_id>')
def view_material(material_id):
    """查看物料详情页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取物料基本信息
        cursor.execute("SELECT * FROM materials WHERE id = %s", (material_id,))
        material = cursor.fetchone()
        
        if not material:
            flash('物料不存在', 'error')
            return redirect(url_for('material_management.index'))
        
        # 获取相关检验记录统计（批量导入检验记录）
        cursor.execute("""
            SELECT
                COUNT(*) as inspection_count,
                SUM(total_quantity) as total_qty,
                SUM(qualified_quantity) as total_qualified,
                SUM(defect_quantity) as total_defects
            FROM batch_inspection_records
            WHERE material_number = %s
        """, (material['material_number'],))
        inspection_stats = cursor.fetchone()

        return render_template('material_management/view_material.html',
                             material=material,
                             inspection_stats=inspection_stats)
        
    except Exception as e:
        flash(f'获取物料详情失败: {str(e)}', 'error')
        return redirect(url_for('material_management.index'))
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
