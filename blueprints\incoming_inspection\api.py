from flask import jsonify, request, current_app
from . import incoming_inspection_bp, full_inspection_bp, sampling_inspection_bp
from db_config import get_db_connection
import os
import sys
import shutil
import platform
from werkzeug.utils import secure_filename
import json
from datetime import datetime, timedelta

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))

# 导入工具函数
try:
    from file_utils import get_safe_upload_path, validate_file_size, get_system_setting
except ImportError:
    # 如果导入失败，定义简单的备用函数
    def get_safe_upload_path(base_path, sub_dirs=None):
        if sub_dirs:
            full_path = os.path.join(base_path, *sub_dirs)
        else:
            full_path = base_path
        os.makedirs(full_path, exist_ok=True)
        return full_path

    def validate_file_size(file, max_size_mb=5):
        return True  # 简单实现

    def get_system_setting(key, default_value=None):
        return default_value
from report_code_generator import generate_report_code

# 物料信息API已移至app.py统一管理，此处API已删除

# ------ 来料检验公共API ------

# 保存全部检验记录API
@full_inspection_bp.route('/api/save_inspection', methods=['POST'])
def save_full_inspection():
    try:
        # 获取基本表单数据
        material_number = request.form.get('material_number')
        material_name = request.form.get('material_name')
        specification = request.form.get('specification')
        material_type = request.form.get('material_type')
        color = request.form.get('color')
        supplier = request.form.get('supplier')
        purchase_order = request.form.get('purchase_order')
        receipt_date = request.form.get('receipt_date')
        inspection_date = request.form.get('inspection_date')
        batch_number = request.form.get('batch_number', '')  # 批次号
        total_quantity = int(request.form.get('total_quantity'))
        defect_quantity = int(request.form.get('defect_quantity'))
        qualified_quantity = int(request.form.get('qualified_quantity'))
        inspector = request.form.get('inspector')
        
        # 处理问题点和图片
        issues_json = request.form.get('issues', '[]')
        issue_data = json.loads(issues_json)
        
        # 处理上传的图片
        for i, issue in enumerate(issue_data):
            issue_images = []
            image_files = request.files.getlist(f'issue_images_{i+1}[]')
            
            for image in image_files:
                if image and image.filename:
                    # 创建保存目录
                    save_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'defect_images',
                                         inspection_date.replace('-', ''), material_number)
                    try:
                        if not os.path.exists(save_dir):
                            os.makedirs(save_dir, exist_ok=True)
                    except Exception as e:
                        print(f"警告：无法创建目录 {save_dir}: {str(e)}")
                        # 使用默认目录
                        save_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'defect_images')
                        if not os.path.exists(save_dir):
                            os.makedirs(save_dir, exist_ok=True)
                        
                    # 安全的文件名
                    filename = secure_filename(f"{i+1}_{image.filename}")
                    filepath = os.path.join(save_dir, filename)
                    
                    # 保存图片
                    try:
                        image.save(filepath)

                        # 记录相对路径
                        if save_dir.endswith(material_number):
                            rel_path = os.path.join('defect_images', inspection_date.replace('-', ''),
                                                 material_number, filename)
                        else:
                            # 如果使用默认目录，则使用简化路径
                            rel_path = os.path.join('defect_images', filename)
                        issue_images.append(rel_path)
                    except Exception as e:
                        print(f"警告：保存图片失败 {filepath}: {str(e)}")
                        continue
            
            issue['images'] = issue_images
        
        # 将不良问题数据转为JSON格式
        defect_issues_json = json.dumps(issue_data, ensure_ascii=False)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 生成报告编码
        report_code = generate_report_code('full', inspection_date)

        try:
            cursor.execute("""
                INSERT INTO full_inspection (
                    report_code,
                    material_number,
                    material_name,
                    specification,
                    material_type,
                    color,
                    supplier,
                    purchase_order,
                    receipt_date,
                    inspection_date,
                    batch_number,
                    total_quantity,
                    qualified_quantity,
                    defect_quantity,
                    defect_issues,
                    inspector
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                report_code,
                material_number,
                material_name,
                specification,
                material_type,
                color,
                supplier,
                purchase_order,
                receipt_date,
                inspection_date,
                batch_number,
                total_quantity,
                qualified_quantity,
                defect_quantity,
                defect_issues_json,
                inspector
            ))
            
            # 获取插入记录的ID
            inspection_id = cursor.lastrowid

            conn.commit()
            return jsonify({
                "success": True,
                "message": "检验记录添加成功",
                "report_code": report_code,
                "inspection_id": inspection_id
            })
        except Exception as e:
            conn.rollback()
            return jsonify({"success": False, "error": str(e)}), 500
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        return jsonify({"success": False, "error": f"处理请求失败: {str(e)}"}), 400

# 保存抽样检验记录API
@sampling_inspection_bp.route('/api/save_inspection', methods=['POST'])
def save_sampling_inspection():
    try:
        # 获取基本表单数据
        material_number = request.form.get('material_number')
        material_name = request.form.get('material_name')
        specification = request.form.get('specification')
        material_type = request.form.get('material_type')
        color = request.form.get('color')
        supplier = request.form.get('supplier')
        purchase_order = request.form.get('purchase_order')
        receipt_date = request.form.get('receipt_date')
        inspection_date = request.form.get('inspection_date')
        batch_number = request.form.get('batch_number', '')  # 批次号
        total_quantity = int(request.form.get('total_quantity'))
        defect_quantity = int(request.form.get('defect_quantity'))
        sample_quantity = int(request.form.get('sample_quantity'))
        inspector = request.form.get('inspector')
        
        # 处理问题点和图片
        issues_json = request.form.get('issues', '[]')
        issue_data = json.loads(issues_json)
        
        # 处理上传的图片
        for i, issue in enumerate(issue_data):
            issue_images = []
            image_files = request.files.getlist(f'issue_images_{i+1}[]')
            
            for image in image_files:
                if image and image.filename:
                    # 创建保存目录
                    save_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'defect_images',
                                         inspection_date.replace('-', ''), material_number)
                    try:
                        if not os.path.exists(save_dir):
                            os.makedirs(save_dir, exist_ok=True)
                    except Exception as e:
                        print(f"警告：无法创建目录 {save_dir}: {str(e)}")
                        # 使用默认目录
                        save_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'defect_images')
                        if not os.path.exists(save_dir):
                            os.makedirs(save_dir, exist_ok=True)
                        
                    # 安全的文件名
                    filename = secure_filename(f"{i+1}_{image.filename}")
                    filepath = os.path.join(save_dir, filename)
                    
                    # 保存图片
                    try:
                        image.save(filepath)

                        # 记录相对路径
                        if save_dir.endswith(material_number):
                            rel_path = os.path.join('defect_images', inspection_date.replace('-', ''),
                                                 material_number, filename)
                        else:
                            # 如果使用默认目录，则使用简化路径
                            rel_path = os.path.join('defect_images', filename)
                        issue_images.append(rel_path)
                    except Exception as e:
                        print(f"警告：保存图片失败 {filepath}: {str(e)}")
                        continue
            
            issue['images'] = issue_images
        
        # 将不良问题数据转为JSON格式
        defect_issues_json = json.dumps(issue_data, ensure_ascii=False)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 计算合格数量（抽样检验中：合格数量 = 抽样数量 - 不良数量）
        qualified_quantity = sample_quantity - defect_quantity

        # 生成报告编码
        report_code = generate_report_code('sampling', inspection_date)

        try:
            cursor.execute("""
                INSERT INTO sampling_inspection (
                    report_code,
                    material_number,
                    material_name,
                    specification,
                    material_type,
                    color,
                    supplier,
                    purchase_order,
                    receipt_date,
                    inspection_date,
                    batch_number,
                    total_quantity,
                    sample_quantity,
                    defect_quantity,
                    qualified_quantity,
                    defect_issues,
                    inspector
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                report_code,
                material_number,
                material_name,
                specification,
                material_type,
                color,
                supplier,
                purchase_order,
                receipt_date,
                inspection_date,
                batch_number,
                total_quantity,
                sample_quantity,
                defect_quantity,
                qualified_quantity,
                defect_issues_json,
                inspector
            ))
            
            # 获取插入记录的ID
            inspection_id = cursor.lastrowid

            conn.commit()
            return jsonify({
                "success": True,
                "message": "检验记录添加成功",
                "report_code": report_code,
                "inspection_id": inspection_id
            })
        except Exception as e:
            conn.rollback()
            return jsonify({"success": False, "error": str(e)}), 500
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        return jsonify({"success": False, "error": f"处理请求失败: {str(e)}"}), 400

# 获取全检记录详情API
@full_inspection_bp.route('/api/inspection_details/<int:record_id>', methods=['GET'])
def get_full_inspection_details(record_id):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        cursor.execute("SELECT * FROM full_inspection WHERE id = %s", (record_id,))
        record = cursor.fetchone()
        
        if record:
            # 转换日期格式以便JSON序列化
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
            
            return jsonify({"success": True, "record": record})
        else:
            return jsonify({"success": False, "error": f"未找到ID为{record_id}的记录"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 获取抽检记录详情API
@sampling_inspection_bp.route('/api/inspection_details/<int:record_id>', methods=['GET'])
def get_sampling_inspection_details(record_id):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        cursor.execute("SELECT * FROM sampling_inspection WHERE id = %s", (record_id,))
        record = cursor.fetchone()
        
        if record:
            # 转换日期格式以便JSON序列化
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
            
            return jsonify({"success": True, "record": record})
        else:
            return jsonify({"success": False, "error": f"未找到ID为{record_id}的记录"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 高级搜索 - 全部检验API
@full_inspection_bp.route('/api/search', methods=['GET'])
def full_advanced_search():
    # 获取搜索参数
    material_number = request.args.get('material_number', '')
    material_name = request.args.get('material_name', '')
    supplier = request.args.get('supplier', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    purchase_order = request.args.get('purchase_order', '')
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        if material_name:
            conditions.append("material_name LIKE %s")
            params.append(f"%{material_name}%")
        
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        if start_date:
            conditions.append("inspection_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("inspection_date <= %s")
            params.append(end_date)
        
        if purchase_order:
            conditions.append("purchase_order LIKE %s")
            params.append(f"%{purchase_order}%")
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 执行查询
        query = f"""
            SELECT * FROM full_inspection
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT 100
        """
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        # 转换日期格式以便JSON序列化
        for record in records:
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({"success": True, "records": records})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 高级搜索 - 抽样检验API
@sampling_inspection_bp.route('/api/search', methods=['GET'])
def sampling_advanced_search():
    # 获取搜索参数
    material_number = request.args.get('material_number', '')
    material_name = request.args.get('material_name', '')
    supplier = request.args.get('supplier', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    purchase_order = request.args.get('purchase_order', '')
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        if material_name:
            conditions.append("material_name LIKE %s")
            params.append(f"%{material_name}%")
        
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        if start_date:
            conditions.append("inspection_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("inspection_date <= %s")
            params.append(end_date)
        
        if purchase_order:
            conditions.append("purchase_order LIKE %s")
            params.append(f"%{purchase_order}%")
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 执行查询
        query = f"""
            SELECT * FROM sampling_inspection
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT 100
        """
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        # 转换日期格式以便JSON序列化
        for record in records:
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({"success": True, "records": records})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 历史问题点查询API
@incoming_inspection_bp.route('/api/history_issues/<material_number>', methods=['GET'])
def get_history_issues(material_number):
    """获取指定物料的历史问题点"""
    try:
        period = request.args.get('period', '1')  # 默认1个月
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 计算时间范围
        if period == 'custom' and start_date and end_date:
            # 使用自定义时间范围
            date_condition = "AND inspection_date BETWEEN %s AND %s"
            date_params = [start_date, end_date]
        else:
            # 使用预设时间范围
            months = int(period)
            end_date_calc = datetime.now()
            start_date_calc = end_date_calc - timedelta(days=months * 30)
            date_condition = "AND inspection_date BETWEEN %s AND %s"
            date_params = [start_date_calc.strftime('%Y-%m-%d'), end_date_calc.strftime('%Y-%m-%d')]

        # 查询抽样检验历史问题
        sampling_query = f"""
            SELECT
                inspection_date,
                defect_issues,
                supplier,
                'sampling' as inspection_type
            FROM sampling_inspection
            WHERE material_number = %s
            {date_condition}
            AND defect_issues IS NOT NULL
            AND defect_issues != ''
            ORDER BY inspection_date DESC
            LIMIT 100
        """

        cursor.execute(sampling_query, [material_number] + date_params)
        sampling_records = cursor.fetchall()

        # 查询全部检验历史问题
        full_query = f"""
            SELECT
                inspection_date,
                defect_issues,
                supplier,
                'full' as inspection_type
            FROM full_inspection
            WHERE material_number = %s
            {date_condition}
            AND defect_issues IS NOT NULL
            AND defect_issues != ''
            ORDER BY inspection_date DESC
            LIMIT 100
        """

        cursor.execute(full_query, [material_number] + date_params)
        full_records = cursor.fetchall()

        # 合并并解析问题点数据
        all_issues = []

        for record in sampling_records + full_records:
            try:
                if record['defect_issues']:
                    issues_data = json.loads(record['defect_issues'])
                    for issue in issues_data:
                        all_issues.append({
                            'inspection_date': record['inspection_date'].strftime('%Y-%m-%d'),
                            'issue_type': issue.get('type', ''),
                            'issue_description': issue.get('description', ''),
                            'supplier': record.get('supplier', ''),
                            'inspection_type': '抽样检验' if record['inspection_type'] == 'sampling' else '全部检验'
                        })
            except (json.JSONDecodeError, KeyError):
                continue

        # 按日期排序
        all_issues.sort(key=lambda x: x['inspection_date'], reverse=True)

        return jsonify({
            "success": True,
            "issues": all_issues[:50]  # 增加返回数量
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 附件信息查询API
@incoming_inspection_bp.route('/api/attachments/<material_number>', methods=['GET'])
def get_attachments(material_number):
    """获取指定物料的相关附件"""
    try:
        # 从系统设置中获取文档路径
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = 'document_path'")
        result = cursor.fetchone()

        if result and result['setting_value']:
            base_path = result['setting_value']
        else:
            base_path = "D:\\Documents\\Materials"  # 默认文档路径

        cursor.close()
        conn.close()

        material_folder = os.path.join(base_path, material_number)
        attachments = []

        if os.path.exists(material_folder):
            for filename in os.listdir(material_folder):
                file_path = os.path.join(material_folder, filename)
                if os.path.isfile(file_path):
                    # 获取文件扩展名
                    _, extension = os.path.splitext(filename)
                    extension = extension.lower().lstrip('.')

                    attachments.append({
                        'filename': filename,
                        'extension': extension,
                        'path': file_path,
                        'size': os.path.getsize(file_path),
                        'modified_time': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                    })

        # 按修改时间排序
        attachments.sort(key=lambda x: x['modified_time'], reverse=True)

        return jsonify({
            "success": True,
            "attachments": attachments
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@sampling_inspection_bp.route('/api/download_to_local', methods=['POST'])
def download_attachment_to_local():
    """下载附件到本地指定路径"""
    try:
        data = request.get_json()
        source_file_path = data.get('source_file_path')
        local_path = data.get('local_path', 'C:\\QMS1\\')

        if not source_file_path:
            return jsonify({"success": False, "error": "源文件路径不能为空"}), 400

        if not os.path.exists(source_file_path):
            return jsonify({"success": False, "error": "源文件不存在"}), 404

        # 确保本地路径存在
        os.makedirs(local_path, exist_ok=True)

        # 获取文件名
        file_name = os.path.basename(source_file_path)

        # 目标文件路径
        target_file = os.path.join(local_path, file_name)

        # 复制文件到本地路径
        shutil.copy2(source_file_path, target_file)

        # 验证文件是否复制成功
        if os.path.exists(target_file):
            return jsonify({
                "success": True,
                "message": "文件下载成功",
                "local_file_path": target_file,
                "file_name": file_name,
                "file_size": os.path.getsize(target_file)
            })
        else:
            return jsonify({"success": False, "error": "文件复制失败"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@sampling_inspection_bp.route('/api/open_local_file', methods=['POST'])
def open_local_file():
    """打开本地文件"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path:
            return jsonify({"success": False, "error": "文件路径不能为空"}), 400

        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"}), 404

        # 根据操作系统使用不同的方法打开文件
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                os.system(f'open "{file_path}"')
            else:  # Linux
                os.system(f'xdg-open "{file_path}"')

            return jsonify({
                "success": True,
                "message": f"文件已打开: {os.path.basename(file_path)}"
            })
        except Exception as e:
            return jsonify({"success": False, "error": f"打开文件失败: {str(e)}"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500