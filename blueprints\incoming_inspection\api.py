﻿# -*- coding: utf-8 -*-
"""
来料检验API - 批量导入检验功能
"""

from flask import jsonify, request
from . import incoming_inspection_bp
from db_config import get_db_connection
import os
import sys
from datetime import datetime
import json

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))

# 批量导入检验记录API
@incoming_inspection_bp.route('/api/batch_import_inspection', methods=['POST'])
def batch_import_inspection():
    """批量导入检验记录"""
    try:
        data = request.get_json()
        inspection_records = data.get('records', [])
        
        if not inspection_records:
            return jsonify({"success": False, "error": "没有提供检验记录数据"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        success_count = 0
        error_items = []
        batch_name = data.get('batch_name', f"批量导入_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        for record in inspection_records:
            try:
                # 验证必填字段
                required_fields = ['material_number', 'material_name', 'inspection_date', 
                                 'total_quantity', 'qualified_quantity', 'defect_quantity']
                
                for field in required_fields:
                    if not record.get(field):
                        raise ValueError(f"缺少必填字段: {field}")
                
                # 插入检验记录
                cursor.execute("""
                    INSERT INTO batch_inspection_records 
                    (material_number, material_name, specification, material_type, color,
                     supplier, purchase_order, receipt_date, inspection_date, 
                     total_quantity, qualified_quantity, defect_quantity, 
                     defect_issues, inspector, batch_name)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    record.get('material_number'),
                    record.get('material_name'),
                    record.get('specification', ''),
                    record.get('material_type', ''),
                    record.get('color', ''),
                    record.get('supplier', ''),
                    record.get('purchase_order', ''),
                    record.get('receipt_date'),
                    record.get('inspection_date'),
                    int(record.get('total_quantity', 0)),
                    int(record.get('qualified_quantity', 0)),
                    int(record.get('defect_quantity', 0)),
                    record.get('defect_issues', ''),
                    record.get('inspector', ''),
                    batch_name
                ))
                
                success_count += 1
                
            except Exception as e:
                error_items.append({
                    "material_number": record.get('material_number', '未知'),
                    "error": str(e)
                })
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "message": f"批量导入完成，成功导入 {success_count} 条检验记录",
            "data": {
                "batch_name": batch_name,
                "success_count": success_count,
                "error_count": len(error_items),
                "error_items": error_items
            }
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取批量导入检验记录列表API
@incoming_inspection_bp.route('/api/batch_inspection_records', methods=['GET'])
def get_batch_inspection_records():
    """获取批量导入的检验记录列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        batch_name = request.args.get('batch_name', '')
        material_number = request.args.get('material_number', '')
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if batch_name:
            where_conditions.append("batch_name LIKE %s")
            params.append(f"%{batch_name}%")
        
        if material_number:
            where_conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 查询总数
        count_query = f"SELECT COUNT(*) as total FROM batch_inspection_records {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查询记录
        offset = (page - 1) * per_page
        query = f"""
            SELECT * FROM batch_inspection_records 
            {where_clause}
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, params + [per_page, offset])
        records = cursor.fetchall()
        
        # 转换日期格式
        for record in records:
            if record.get('receipt_date'):
                record['receipt_date'] = record['receipt_date'].isoformat()
            if record.get('inspection_date'):
                record['inspection_date'] = record['inspection_date'].isoformat()
            if record.get('created_at'):
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({
            "success": True,
            "records": records,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
