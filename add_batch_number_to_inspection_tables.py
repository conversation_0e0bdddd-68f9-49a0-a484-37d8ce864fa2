#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为检验记录表添加批次号字段
"""

import sys
import traceback

def add_batch_number_fields():
    try:
        print("1. 导入数据库配置...")
        from db_config import get_db_connection
        
        print("2. 建立数据库连接...")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("3. 检查检验记录表是否存在...")
        cursor.execute("SHOW TABLES LIKE 'sampling_inspection'")
        sampling_table_exists = cursor.fetchone()
        
        cursor.execute("SHOW TABLES LIKE 'full_inspection'")
        full_table_exists = cursor.fetchone()
        
        if not sampling_table_exists:
            print("❌ sampling_inspection表不存在")
            return False
            
        if not full_table_exists:
            print("❌ full_inspection表不存在")
            return False
        
        print("✅ 检验记录表存在")
        
        # 检查抽样检验表结构
        print("4. 检查抽样检验表结构...")
        cursor.execute("DESCRIBE sampling_inspection")
        sampling_columns = cursor.fetchall()
        
        sampling_column_names = [col[0] for col in sampling_columns]
        print("抽样检验表当前字段:", sampling_column_names)
        
        # 检查全部检验表结构
        print("5. 检查全部检验表结构...")
        cursor.execute("DESCRIBE full_inspection")
        full_columns = cursor.fetchall()
        
        full_column_names = [col[0] for col in full_columns]
        print("全部检验表当前字段:", full_column_names)
        
        # 为抽样检验表添加批次号字段
        if 'batch_number' not in sampling_column_names:
            print("6. 为抽样检验表添加批次号字段...")
            cursor.execute("""
                ALTER TABLE sampling_inspection 
                ADD COLUMN batch_number VARCHAR(100) COMMENT '批次号' AFTER inspection_date
            """)
            print("✅ 抽样检验表批次号字段添加成功")
        else:
            print("✅ 抽样检验表批次号字段已存在")
        
        # 为全部检验表添加批次号字段
        if 'batch_number' not in full_column_names:
            print("7. 为全部检验表添加批次号字段...")
            cursor.execute("""
                ALTER TABLE full_inspection 
                ADD COLUMN batch_number VARCHAR(100) COMMENT '批次号' AFTER inspection_date
            """)
            print("✅ 全部检验表批次号字段添加成功")
        else:
            print("✅ 全部检验表批次号字段已存在")
        
        # 添加索引
        print("8. 添加索引...")
        try:
            cursor.execute("ALTER TABLE sampling_inspection ADD INDEX idx_batch_number (batch_number)")
            print("✅ 抽样检验表批次号索引添加成功")
        except Exception as e:
            if "Duplicate key name" in str(e):
                print("✅ 抽样检验表批次号索引已存在")
            else:
                print(f"⚠️ 抽样检验表索引添加失败: {e}")
        
        try:
            cursor.execute("ALTER TABLE full_inspection ADD INDEX idx_batch_number (batch_number)")
            print("✅ 全部检验表批次号索引添加成功")
        except Exception as e:
            if "Duplicate key name" in str(e):
                print("✅ 全部检验表批次号索引已存在")
            else:
                print(f"⚠️ 全部检验表索引添加失败: {e}")
        
        conn.commit()
        
        print("9. 验证字段是否添加成功...")
        cursor.execute("DESCRIBE sampling_inspection")
        new_sampling_columns = cursor.fetchall()
        
        cursor.execute("DESCRIBE full_inspection")
        new_full_columns = cursor.fetchall()
        
        print("更新后的抽样检验表结构:")
        for col in new_sampling_columns:
            print(f"  {col[0]} - {col[1]}")
        
        print("更新后的全部检验表结构:")
        for col in new_full_columns:
            print(f"  {col[0]} - {col[1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        print("错误详情:")
        traceback.print_exc()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    print("=== 为检验记录表添加批次号字段 ===")
    success = add_batch_number_fields()
    
    if success:
        print("\n🎉 操作完成！")
    else:
        print("\n❌ 操作失败！")
        sys.exit(1)
