#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除 full_inspection 表的脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_config import get_db_connection
import mysql.connector

def drop_full_inspection_table():
    """删除 full_inspection 表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🗑️  开始删除 full_inspection 表...")
        
        # 删除相关索引
        indexes_to_drop = [
            "idx_full_material_number",
            "idx_full_supplier", 
            "idx_full_dates",
            "idx_full_search"
        ]
        
        for index_name in indexes_to_drop:
            try:
                cursor.execute(f"DROP INDEX {index_name} ON full_inspection")
                print(f"✅ 索引 {index_name} 删除成功")
            except mysql.connector.Error as e:
                if e.errno == 1091:  # 索引不存在
                    print(f"⚠️  索引 {index_name} 不存在，跳过")
                else:
                    print(f"❌ 删除索引 {index_name} 失败: {e}")
        
        # 删除表
        try:
            cursor.execute("DROP TABLE IF EXISTS full_inspection")
            print("✅ full_inspection 表删除成功")
        except mysql.connector.Error as e:
            print(f"❌ 删除表失败: {e}")
            return False
        
        conn.commit()
        print("✅ 删除操作完成")
        return True
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始删除 full_inspection 表")
    print("=" * 50)
    
    if drop_full_inspection_table():
        print("=" * 50)
        print("🎉 full_inspection 表删除完成！")
    else:
        print("=" * 50)
        print("❌ 删除失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
