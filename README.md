# 品质中心管理系统

## 📋 项目简介

品质中心管理系统是一个基于Flask开发的来料检验管理平台，专门为品控部门设计，用于管理和记录物料的质量检验流程。

## 🚀 主要功能

### 核心模块
- **来料检验**: 抽样检验、全部检验、检验记录管理
- **物料管理**: 物料信息管理、附件上传、高级搜索
- **过程控制**: 车间异常记录、工艺流程监控
- **尺寸测量**: 尺寸检验记录、测量数据分析
- **系统设置**: 系统参数配置、用户权限管理

### 核心特性
- 智能检验流程和报告生成
- 多格式文件上传和管理
- 高级搜索和数据分析
- 响应式Web界面

## 🛠️ 技术栈

- **后端**: Flask 2.3.3, MySQL, Python 3.8+
- **前端**: HTML5/CSS3, JavaScript, Bootstrap
- **数据库**: MySQL 5.7+ / 8.0+

## 📦 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置
```bash
# 配置数据库连接（修改config.py）
DB_HOST = 'localhost'
DB_USER = 'root'
DB_PASSWORD = 'your_password'
DB_NAME = 'quality_control'

# 初始化数据库
python database/create_tables.py
python database/generate_sample_data.py
```

### 3. 启动应用
```bash
python app.py
```

访问 http://localhost:5000 开始使用系统。

## 📁 项目结构

```
品控部/
├── app.py                 # 主应用入口
├── config.py             # 配置管理
├── db_config.py          # 数据库配置
├── blueprints/           # 功能模块
│   ├── incoming_inspection/  # 来料检验
│   ├── material_management/  # 物料管理
│   ├── process_control/      # 过程控制
│   ├── dimension_measurement/# 尺寸测量
│   └── system_settings/      # 系统设置
├── database/             # 数据库脚本
├── static/              # 静态资源
├── templates/           # 页面模板
├── utils/               # 工具函数
└── requirements.txt     # 依赖包
```

## 🗄️ 数据库表结构

- `sampling_inspection` - 抽样检验记录
- `materials` - 物料信息
- `material_attachments` - 物料附件
- `workshop_abnormal` - 车间异常记录
- `system_settings` - 系统设置

## 🔧 开发环境

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 代码格式化
black .
isort .
flake8 .
```

## 📝 使用说明

1. **物料管理**: 录入物料基本信息，上传相关附件
2. **检验安排**: 进行抽样检验质量检验
3. **记录检验**: 记录检验结果、缺陷信息和合格数量
4. **报告生成**: 系统自动生成检验报告编号
5. **数据分析**: 查看质量趋势和统计分析

## 🔒 安全特性

- 文件上传安全验证
- SQL注入防护
- 用户输入验证
- 安全的文件名处理

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: 1.0.0  
**更新时间**: 2025-01-25
