#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例数据生成脚本
用于生成品质中心管理系统的测试数据
"""

import random
import json
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config
import mysql.connector

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'utils'))
from report_code_generator import generate_report_code

def get_db_connection():
    """建立与数据库的连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def generate_materials_data():
    """生成物料基础数据"""
    materials = [
        ('ABC001', '不锈钢螺栓', '10x50mm', '304不锈钢', '银色'),
        ('ABC002', '铝合金外壳', '100x80x20mm', '6061铝合金', '黑色'),
        ('ABC003', '塑料连接器', '15x10x5mm', 'ABS塑料', '白色'),
        ('ABC004', '橡胶密封圈', 'Φ20x2mm', '硅橡胶', '黑色'),
        ('ABC005', '钢制弹簧', 'Φ8x30mm', '弹簧钢', '银色'),
        ('DEF001', '铜制端子', '8x6x3mm', '紫铜', '金色'),
        ('DEF002', '玻璃面板', '200x150x3mm', '钢化玻璃', '透明'),
        ('DEF003', '陶瓷绝缘子', 'Φ12x8mm', '氧化铝陶瓷', '白色'),
        ('GHI001', '碳钢轴承', 'Φ25x8mm', '碳钢', '银色'),
        ('GHI002', '塑料齿轮', 'Φ30x10mm', '尼龙', '白色'),
        ('JKL001', '铝制散热片', '50x40x15mm', '6063铝合金', '银色'),
        ('JKL002', '硅胶垫片', '20x20x2mm', '硅胶', '透明'),
        ('MNO001', '钛合金螺丝', '6x25mm', 'TC4钛合金', '银色'),
        ('MNO002', '聚氨酯泡沫', '100x100x10mm', '聚氨酯', '黄色'),
        ('PQR001', '镁合金支架', '80x60x5mm', 'AZ31镁合金', '银色'),
    ]
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始导入物料基础数据...")
        
        # 插入物料数据
        for material in materials:
            cursor.execute("""
                INSERT IGNORE INTO materials (material_number, material_name, specification, material_type, color)
                VALUES (%s, %s, %s, %s, %s)
            """, material)
        
        conn.commit()
        print(f"✅ 成功导入 {len(materials)} 种物料基础数据")
        return True
        
    except Exception as e:
        print(f"❌ 物料数据导入失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def generate_inspection_data():
    """生成检验记录数据"""
    # 物料基础数据
    materials = [
        ('ABC001', '不锈钢螺栓', '10x50mm', '304不锈钢', '银色'),
        ('ABC002', '铝合金外壳', '100x80x20mm', '6061铝合金', '黑色'),
        ('ABC003', '塑料连接器', '15x10x5mm', 'ABS塑料', '白色'),
        ('ABC004', '橡胶密封圈', 'Φ20x2mm', '硅橡胶', '黑色'),
        ('ABC005', '钢制弹簧', 'Φ8x30mm', '弹簧钢', '银色'),
        ('DEF001', '铜制端子', '8x6x3mm', '紫铜', '金色'),
        ('DEF002', '玻璃面板', '200x150x3mm', '钢化玻璃', '透明'),
        ('DEF003', '陶瓷绝缘子', 'Φ12x8mm', '氧化铝陶瓷', '白色'),
        ('GHI001', '碳钢轴承', 'Φ25x8mm', '碳钢', '银色'),
        ('GHI002', '塑料齿轮', 'Φ30x10mm', '尼龙', '白色'),
    ]
    
    # 供应商列表
    suppliers = ['东莞精密制造有限公司', '深圳科技实业股份', '苏州工业园区制造厂', '上海精工机械公司', '广州质量器材厂', '北京高新技术公司', '天津制造集团', '重庆精密工业']
    
    # 检验员列表
    inspectors = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
    
    # 问题类型和描述
    issue_types = {
        '尺寸不良': [
            '长度超出公差范围，实测值偏大',
            '宽度尺寸不符合要求，偏小0.2mm',
            '厚度测量值超标，影响装配',
            '直径尺寸偏差较大，不符合图纸要求',
            '孔径尺寸不准确，影响配合精度'
        ],
        '外观不良': [
            '表面有明显划痕，影响外观质量',
            '颜色不均匀，存在色差问题',
            '表面氧化严重，影响防腐性能',
            '有毛刺未处理干净，影响手感',
            '表面有污渍，清洁度不够'
        ],
        '功能不良': [
            '连接器接触不良，导致信号异常',
            '密封性能不达标，存在泄漏',
            '弹性不足，回弹性能差',
            '导电性能不符合要求',
            '绝缘性能测试不合格'
        ],
        '包装不良': [
            '包装破损严重，内部产品受损',
            '包装材料不符合环保要求',
            '标识不清晰，无法识别型号',
            '包装尺寸不合适，浪费空间',
            '防潮包装失效，产品受潮'
        ],
        '其他': [
            '材质证明文件缺失',
            '检验报告数据异常',
            '生产日期标识错误',
            '批次信息不完整',
            '技术文件版本过期'
        ]
    }
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始生成检验记录数据...")
        
        # 生成抽样检验数据
        sampling_count = 0
        for i in range(150):
            material = random.choice(materials)
            material_number = material[0]
            material_name = material[1]
            specification = material[2]
            material_type = material[3]
            color = material[4]
            
            supplier = random.choice(suppliers)
            inspector = random.choice(inspectors)
            
            # 随机生成日期（最近6个月）
            days_ago = random.randint(1, 180)
            inspection_date = datetime.now() - timedelta(days=days_ago)
            receipt_date = inspection_date - timedelta(days=random.randint(0, 3))
            
            total_quantity = random.randint(100, 2000)
            sample_quantity = random.randint(10, min(100, total_quantity // 5))
            defect_quantity = random.randint(0, min(5, sample_quantity // 3))
            qualified_quantity = sample_quantity - defect_quantity
            
            # 生成问题点数据
            issues = []
            if defect_quantity > 0:
                num_issues = random.randint(1, min(3, defect_quantity))
                for _ in range(num_issues):
                    issue_type = random.choice(list(issue_types.keys()))
                    issue_desc = random.choice(issue_types[issue_type])
                    issues.append({
                        'type': issue_type,
                        'description': issue_desc
                    })
            
            # 生成报告编码
            report_code = generate_report_code('sampling', inspection_date)

            cursor.execute("""
                INSERT INTO sampling_inspection (
                    report_code, material_number, material_name, specification, material_type, color,
                    supplier, purchase_order, receipt_date, inspection_date,
                    total_quantity, sample_quantity, qualified_quantity, defect_quantity, inspector,
                    defect_issues, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, f'PO{inspection_date.strftime("%Y%m%d")}{i:03d}',
                receipt_date.strftime('%Y-%m-%d'),
                inspection_date.strftime('%Y-%m-%d'),
                total_quantity, sample_quantity, qualified_quantity, defect_quantity, inspector,
                json.dumps(issues, ensure_ascii=False) if issues else None,
                inspection_date
            ))
            sampling_count += 1
        

        
        conn.commit()
        print(f"✅ 成功生成 {sampling_count} 条抽样检验记录")
        return True
        
    except Exception as e:
        print(f"❌ 检验数据生成失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始生成示例数据")
    print("=" * 50)
    
    # 1. 生成物料基础数据
    if not generate_materials_data():
        print("❌ 物料数据生成失败，程序退出")
        return False
    
    # 2. 生成检验记录数据
    if not generate_inspection_data():
        print("❌ 检验数据生成失败，程序退出")
        return False
    
    print("=" * 50)
    print("🎉 示例数据生成完成！")
    print("\n📊 生成的数据统计:")
    print("  - 物料基础数据: 15种")
    print("  - 抽样检验记录: 150条")
    print("  - 全部检验记录: 100条")
    print("  - 总计检验记录: 250条")
    print("\n💡 使用说明:")
    print("  - 数据时间范围: 最近6个月")
    print("  - 包含多种问题类型和描述")
    print("  - 支持历史问题点查询功能测试")
    
    return True

if __name__ == "__main__":
    main()
