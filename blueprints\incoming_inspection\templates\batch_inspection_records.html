<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入检验记录 - 品质中心管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }
        .pagination-info {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-top: 20px;
        }
        .badge-custom {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-list"></i> 批量导入检验记录</h2>
                    <div>
                        <a href="{{ url_for('incoming_inspection.batch_import_inspection_page') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增导入
                        </a>
                        <a href="{{ url_for('incoming_inspection.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 搜索区域 -->
                <div class="search-section">
                    <h5><i class="fas fa-search"></i> 搜索条件</h5>
                    <form id="search-form">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="batch-name" class="form-label">批次名称</label>
                                <input type="text" class="form-control" id="batch-name" name="batch_name" placeholder="输入批次名称">
                            </div>
                            <div class="col-md-3">
                                <label for="material-number" class="form-label">物料料号</label>
                                <input type="text" class="form-control" id="material-number" name="material_number" placeholder="输入物料料号">
                            </div>
                            <div class="col-md-3">
                                <label for="inspection-date-start" class="form-label">检验日期（开始）</label>
                                <input type="date" class="form-control" id="inspection-date-start" name="inspection_date_start">
                            </div>
                            <div class="col-md-3">
                                <label for="inspection-date-end" class="form-label">检验日期（结束）</label>
                                <input type="date" class="form-control" id="inspection-date-end" name="inspection_date_end">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-secondary" id="reset-btn">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="btn btn-success" id="export-btn">
                                    <i class="fas fa-download"></i> 导出Excel
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-3" id="stats-section">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" id="total-records">0</h5>
                                <p class="card-text">总记录数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success" id="total-qualified">0</h5>
                                <p class="card-text">合格总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger" id="total-defect">0</h5>
                                <p class="card-text">不良总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info" id="qualified-rate">0%</h5>
                                <p class="card-text">合格率</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 记录列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-table"></i> 检验记录列表</h5>
                        <span class="badge bg-primary" id="record-count">0 条记录</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="records-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>批次名称</th>
                                        <th>物料料号</th>
                                        <th>物料名称</th>
                                        <th>供应商</th>
                                        <th>检验日期</th>
                                        <th>来料数量</th>
                                        <th>合格数量</th>
                                        <th>不良数量</th>
                                        <th>合格率</th>
                                        <th>检验员</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="records-tbody">
                                    <tr>
                                        <td colspan="13" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">正在加载数据...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-info">
                            <div>
                                <span id="page-info">第 1 页，共 1 页</span>
                            </div>
                            <nav>
                                <ul class="pagination" id="pagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" id="prev-page">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" id="next-page">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detail-modal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle"></i> 检验记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detail-content">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentData = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            loadRecords();
        });

        function initializeEventListeners() {
            // 搜索表单
            document.getElementById('search-form').addEventListener('submit', function(e) {
                e.preventDefault();
                currentPage = 1;
                loadRecords();
            });

            // 重置按钮
            document.getElementById('reset-btn').addEventListener('click', function() {
                document.getElementById('search-form').reset();
                currentPage = 1;
                loadRecords();
            });

            // 导出按钮
            document.getElementById('export-btn').addEventListener('click', exportToExcel);

            // 分页按钮
            document.getElementById('prev-page').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    loadRecords();
                }
            });

            document.getElementById('next-page').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    loadRecords();
                }
            });
        }

        async function loadRecords() {
            try {
                showLoading();
                
                const formData = new FormData(document.getElementById('search-form'));
                const params = new URLSearchParams();
                
                params.append('page', currentPage);
                params.append('per_page', 20);
                
                for (let [key, value] of formData.entries()) {
                    if (value) {
                        params.append(key, value);
                    }
                }

                const response = await fetch(`/incoming/api/batch_inspection_records?${params}`);
                const result = await response.json();

                if (result.success) {
                    displayRecords(result.records);
                    updatePagination(result.pagination);
                    updateStats(result.records);
                } else {
                    showToast('加载数据失败：' + result.error, 'error');
                }
            } catch (error) {
                showToast('加载数据失败：' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        function displayRecords(records) {
            const tbody = document.getElementById('records-tbody');
            currentData = records;
            
            if (records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="13" class="text-center">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p>暂无数据</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = records.map((record, index) => {
                const qualifiedRate = record.total_quantity > 0 ? 
                    ((record.qualified_quantity / record.total_quantity) * 100).toFixed(1) : '0.0';
                
                return `
                    <tr>
                        <td>${(currentPage - 1) * 20 + index + 1}</td>
                        <td><span class="badge bg-info badge-custom">${record.batch_name}</span></td>
                        <td><strong>${record.material_number}</strong></td>
                        <td>${record.material_name}</td>
                        <td>${record.supplier || '-'}</td>
                        <td>${record.inspection_date}</td>
                        <td><span class="badge bg-primary">${record.total_quantity}</span></td>
                        <td><span class="badge bg-success">${record.qualified_quantity}</span></td>
                        <td><span class="badge bg-danger">${record.defect_quantity}</span></td>
                        <td>
                            <span class="badge ${qualifiedRate >= 95 ? 'bg-success' : qualifiedRate >= 90 ? 'bg-warning' : 'bg-danger'}">
                                ${qualifiedRate}%
                            </span>
                        </td>
                        <td>${record.inspector || '-'}</td>
                        <td>${new Date(record.created_at).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="showDetail(${record.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            document.getElementById('record-count').textContent = `${records.length} 条记录`;
        }

        function updatePagination(pagination) {
            currentPage = pagination.page;
            totalPages = pagination.pages;
            
            document.getElementById('page-info').textContent = 
                `第 ${pagination.page} 页，共 ${pagination.pages} 页（总计 ${pagination.total} 条记录）`;
            
            // 更新分页按钮状态
            const prevBtn = document.getElementById('prev-page').parentElement;
            const nextBtn = document.getElementById('next-page').parentElement;
            
            prevBtn.classList.toggle('disabled', currentPage <= 1);
            nextBtn.classList.toggle('disabled', currentPage >= totalPages);
        }

        function updateStats(records) {
            const totalRecords = records.length;
            const totalQualified = records.reduce((sum, record) => sum + record.qualified_quantity, 0);
            const totalDefect = records.reduce((sum, record) => sum + record.defect_quantity, 0);
            const totalQuantity = records.reduce((sum, record) => sum + record.total_quantity, 0);
            const qualifiedRate = totalQuantity > 0 ? ((totalQualified / totalQuantity) * 100).toFixed(1) : '0.0';

            document.getElementById('total-records').textContent = totalRecords;
            document.getElementById('total-qualified').textContent = totalQualified;
            document.getElementById('total-defect').textContent = totalDefect;
            document.getElementById('qualified-rate').textContent = qualifiedRate + '%';
        }

        function showDetail(recordId) {
            const record = currentData.find(r => r.id === recordId);
            if (!record) return;

            const detailContent = document.getElementById('detail-content');
            detailContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>物料料号：</strong></td><td>${record.material_number}</td></tr>
                            <tr><td><strong>物料名称：</strong></td><td>${record.material_name}</td></tr>
                            <tr><td><strong>规格：</strong></td><td>${record.specification || '-'}</td></tr>
                            <tr><td><strong>材质：</strong></td><td>${record.material_type || '-'}</td></tr>
                            <tr><td><strong>颜色：</strong></td><td>${record.color || '-'}</td></tr>
                            <tr><td><strong>供应商：</strong></td><td>${record.supplier || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>检验信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>采购单号：</strong></td><td>${record.purchase_order || '-'}</td></tr>
                            <tr><td><strong>来料日期：</strong></td><td>${record.receipt_date || '-'}</td></tr>
                            <tr><td><strong>检验日期：</strong></td><td>${record.inspection_date}</td></tr>
                            <tr><td><strong>来料数量：</strong></td><td>${record.total_quantity}</td></tr>
                            <tr><td><strong>合格数量：</strong></td><td>${record.qualified_quantity}</td></tr>
                            <tr><td><strong>不良数量：</strong></td><td>${record.defect_quantity}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6>其他信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>问题描述：</strong></td><td>${record.defect_issues || '-'}</td></tr>
                            <tr><td><strong>检验员：</strong></td><td>${record.inspector || '-'}</td></tr>
                            <tr><td><strong>批次名称：</strong></td><td>${record.batch_name}</td></tr>
                            <tr><td><strong>创建时间：</strong></td><td>${new Date(record.created_at).toLocaleString()}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('detail-modal')).show();
        }

        function exportToExcel() {
            if (currentData.length === 0) {
                showToast('没有数据可导出', 'warning');
                return;
            }

            const exportData = currentData.map(record => ({
                '批次名称': record.batch_name,
                '物料料号': record.material_number,
                '物料名称': record.material_name,
                '规格': record.specification || '',
                '材质': record.material_type || '',
                '颜色': record.color || '',
                '供应商': record.supplier || '',
                '采购单号': record.purchase_order || '',
                '来料日期': record.receipt_date || '',
                '检验日期': record.inspection_date,
                '来料数量': record.total_quantity,
                '合格数量': record.qualified_quantity,
                '不良数量': record.defect_quantity,
                '合格率': record.total_quantity > 0 ? ((record.qualified_quantity / record.total_quantity) * 100).toFixed(1) + '%' : '0.0%',
                '问题描述': record.defect_issues || '',
                '检验员': record.inspector || '',
                '创建时间': new Date(record.created_at).toLocaleString()
            }));

            const ws = XLSX.utils.json_to_sheet(exportData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "批量导入检验记录");
            XLSX.writeFile(wb, `批量导入检验记录_${new Date().toISOString().slice(0, 10)}.xlsx`);
            
            showToast('导出成功', 'success');
        }

        function showLoading() {
            const tbody = document.getElementById('records-tbody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="13" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }

        function hideLoading() {
            // Loading state is handled by displayRecords
        }

        function showToast(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const toast = document.createElement('div');
            toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }
    </script>
</body>
</html>
