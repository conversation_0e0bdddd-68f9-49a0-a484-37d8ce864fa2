from flask import render_template, request, jsonify, current_app
from . import incoming_inspection_bp, sampling_inspection_bp
from db_config import get_db_connection
import os
import sys
from werkzeug.utils import secure_filename
import json
from datetime import datetime, timedelta

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))
from report_code_generator import generate_report_code

# ------ 来料检验主蓝图路由 ------

# 来料检验首页
@incoming_inspection_bp.route('/')
def index():
    return render_template('index.html')

# 智能批量导入页面
@incoming_inspection_bp.route('/smart_batch_import')
def smart_batch_import():
    """智能批量导入页面"""
    return render_template('smart_batch_import.html')

# 新增检验记录页面
@incoming_inspection_bp.route('/new_inspection')
def new_inspection():
    return render_template('new_inspection.html')

# 检验记录详情页面
@incoming_inspection_bp.route('/detail_inspection')
def detail_inspection_page():
    record_id = request.args.get('id')
    inspection_type = request.args.get('type', 'full')
    if not record_id:
        return "缺少记录ID", 400
    return render_template('detail_inspection.html',
                          record_id=record_id,
                          inspection_type=inspection_type)

# 统一批量导入待检页面
@incoming_inspection_bp.route('/batch_import_sampling')
def batch_import_sampling():
    return render_template('batch_import_sampling.html')

# 批量导入待检 - 全部检验（重定向到统一页面）
@incoming_inspection_bp.route('/batch_import_full')
def batch_import_full():
    return render_template('batch_import_sampling.html')

# 待检清单
@incoming_inspection_bp.route('/pending_list')
def pending_list():
    inspection_type = request.args.get('type', 'sampling')
    return render_template('pending_list.html', inspection_type=inspection_type)

# 添加检验记录API
@incoming_inspection_bp.route('/api/add_inspection', methods=['POST'])
def add_inspection():
    try:
        # 获取基本表单数据
        inspection_type = request.form.get('inspection_type', 'full')  # 默认为全部检验
        material_number = request.form.get('material_number')
        material_name = request.form.get('material_name')
        specification = request.form.get('specification')
        material_type = request.form.get('material_type')
        color = request.form.get('color')
        supplier = request.form.get('supplier')
        purchase_order = request.form.get('purchase_order')
        receipt_date = request.form.get('receipt_date')
        inspection_date = request.form.get('inspection_date')
        batch_number = request.form.get('batch_number', '')  # 批次号
        total_quantity = int(request.form.get('total_quantity'))
        defect_quantity = int(request.form.get('defect_quantity'))

        # 根据检验类型获取不同的数量字段
        if inspection_type == 'sampling':
            sample_quantity = int(request.form.get('sample_quantity'))
        else:
            qualified_quantity = int(request.form.get('qualified_quantity'))
        defect_issues = request.form.get('defect_issues')
        inspector = request.form.get('inspector')
        
        # 只支持抽样检验
        if inspection_type != 'sampling':
            return jsonify({"success": False, "error": "不支持的检验类型"}), 400

        # 生成报告编码
        report_code = generate_report_code(inspection_type, inspection_date)

        conn = get_db_connection()
        cursor = conn.cursor()

        # 抽样检验需要sample_quantity和qualified_quantity两个字段
        # 计算合格数量 = 抽样数量 - 不良数量
        qualified_quantity = sample_quantity - defect_quantity

        cursor.execute("""
            INSERT INTO sampling_inspection (
                report_code,
                material_number,
                material_name,
                specification,
                material_type,
                color,
                supplier,
                purchase_order,
                receipt_date,
                inspection_date,
                batch_number,
                total_quantity,
                sample_quantity,
                qualified_quantity,
                defect_quantity,
                defect_issues,
                inspector
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code,
            material_number,
            material_name,
            specification,
            material_type,
            color,
            supplier,
            purchase_order,
            receipt_date,
            inspection_date,
            batch_number,
            total_quantity,
            sample_quantity,
            qualified_quantity,
            defect_quantity,
            defect_issues,
            inspector
        ))
        
        # 获取插入记录的ID
        inspection_id = cursor.lastrowid

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            "success": True,
            "message": "检验记录添加成功",
            "report_code": report_code,
            "inspection_id": inspection_id
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# 获取检验记录详情API
@incoming_inspection_bp.route('/api/inspection_details/<inspection_type>/<int:record_id>', methods=['GET'])
def get_inspection_details(inspection_type, record_id):
    try:
        # 只支持抽样检验
        if inspection_type != 'sampling':
            return jsonify({"success": False, "error": "不支持的检验类型"}), 400
        table_name = 'sampling_inspection'
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询记录
        cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (record_id,))
        record = cursor.fetchone()
        
        if record:
            # 转换日期格式以便JSON序列化
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
            
            return jsonify({"success": True, "record": record})
        else:
            return jsonify({"success": False, "error": "未找到记录"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取相邻记录API
@incoming_inspection_bp.route('/api/neighbor_records/<inspection_type>/<int:record_id>', methods=['GET'])
def get_neighbor_records(inspection_type, record_id):
    try:
        # 只支持抽样检验
        if inspection_type != 'sampling':
            return jsonify({"success": False, "error": "不支持的检验类型"}), 400
        table_name = 'sampling_inspection'
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询当前记录的排序位置
        cursor.execute(f"""
            SELECT inspection_date FROM {table_name} WHERE id = %s
        """, (record_id,))
        
        current = cursor.fetchone()
        if not current:
            return jsonify({"success": False, "error": "未找到当前记录"}), 404
        
        current_date = current['inspection_date']
        
        # 查询前一条记录
        cursor.execute(f"""
            SELECT id FROM {table_name}
            WHERE (inspection_date < %s) OR (inspection_date = %s AND id < %s)
            ORDER BY inspection_date DESC, id DESC
            LIMIT 1
        """, (current_date, current_date, record_id))
        
        prev_record = cursor.fetchone()
        
        # 查询后一条记录
        cursor.execute(f"""
            SELECT id FROM {table_name}
            WHERE (inspection_date > %s) OR (inspection_date = %s AND id > %s)
            ORDER BY inspection_date ASC, id ASC
            LIMIT 1
        """, (current_date, current_date, record_id))
        
        next_record = cursor.fetchone()
        
        return jsonify({
            "success": True,
            "prev_id": prev_record['id'] if prev_record else None,
            "next_id": next_record['id'] if next_record else None
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 高级搜索API
@incoming_inspection_bp.route('/api/search/<string:inspection_type>', methods=['GET'])
def advanced_search(inspection_type):
    # 获取搜索参数
    material_number = request.args.get('material_number', '')
    material_name = request.args.get('material_name', '')
    supplier = request.args.get('supplier', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    purchase_order = request.args.get('purchase_order', '')
    
    # 只支持抽样检验
    if inspection_type != 'sampling':
        return jsonify({"success": False, "error": "不支持的检验类型"}), 400
    table_name = 'sampling_inspection'
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        if material_name:
            conditions.append("material_name LIKE %s")
            params.append(f"%{material_name}%")
        
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        if start_date:
            conditions.append("inspection_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("inspection_date <= %s")
            params.append(end_date)
        
        if purchase_order:
            conditions.append("purchase_order LIKE %s")
            params.append(f"%{purchase_order}%")
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 执行查询
        query = f"""
            SELECT * FROM {table_name}
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT 100
        """
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        # 转换日期格式以便JSON序列化
        for record in records:
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({"success": True, "records": records})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 获取最近检验记录API
@incoming_inspection_bp.route('/api/recent_inspections')
def recent_inspections():
    try:
        days = request.args.get('days', 30, type=int)
        limit = request.args.get('limit', 10, type=int)
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询抽样检验记录
        cursor.execute("""
            SELECT 'sampling' as type, id, material_number, material_name, supplier, inspection_date,
                   total_quantity, sample_quantity as qualified_quantity, defect_quantity
            FROM sampling_inspection
            WHERE inspection_date BETWEEN %s AND %s
            ORDER BY inspection_date DESC
            LIMIT %s
        """, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'), limit))

        all_records = cursor.fetchall()
        
        # 转换日期格式
        for record in all_records:
            record['inspection_date'] = record['inspection_date'].isoformat()
        
        return jsonify({"success": True, "records": all_records})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取最近供应商信息API
@incoming_inspection_bp.route('/api/recent_supplier/<string:material_number>')
def get_recent_supplier(material_number):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询该物料最近的验货记录中的供应商信息
        query = """
        SELECT supplier, inspection_date
        FROM sampling_inspection
        WHERE material_number = %s AND supplier IS NOT NULL AND supplier != ''
        ORDER BY inspection_date DESC
        LIMIT 1
        """

        cursor.execute(query, (material_number,))
        result = cursor.fetchone()

        if result:
            supplier = result[0]
            return jsonify({
                "success": True,
                "supplier": supplier,
                "inspection_date": result[1].strftime('%Y-%m-%d') if result[1] else None
            })
        else:
            return jsonify({
                "success": False,
                "message": "未找到该物料的历史供应商信息"
            })

    except Exception as e:
        current_app.logger.error(f"获取供应商信息失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 检验统计数据API
@incoming_inspection_bp.route('/api/inspection_statistics')
def inspection_statistics():
    try:
        period = request.args.get('period', 'month')  # 默认按月统计
        year = request.args.get('year', datetime.now().year, type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        if period == 'month':
            # 按月统计数据
            cursor.execute("""
                SELECT
                    MONTH(inspection_date) as period,
                    SUM(total_quantity) as total_qty,
                    SUM(sample_quantity) as sample_qty,
                    SUM(defect_quantity) as defect_qty
                FROM sampling_inspection
                WHERE YEAR(inspection_date) = %s
                GROUP BY MONTH(inspection_date)
                ORDER BY MONTH(inspection_date)
            """, (year,))

            sampling_stats = cursor.fetchall()

        elif period == 'quarter':
            # 按季度统计数据
            cursor.execute("""
                SELECT
                    QUARTER(inspection_date) as period,
                    SUM(total_quantity) as total_qty,
                    SUM(sample_quantity) as sample_qty,
                    SUM(defect_quantity) as defect_qty
                FROM sampling_inspection
                WHERE YEAR(inspection_date) = %s
                GROUP BY QUARTER(inspection_date)
                ORDER BY QUARTER(inspection_date)
            """, (year,))

            sampling_stats = cursor.fetchall()

        return jsonify({
            "success": True,
            "sampling_statistics": sampling_stats
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()



# ------ 抽样检验蓝图路由 ------

# 抽样检验页面
@sampling_inspection_bp.route('/')
def index():
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 获取筛选参数
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    receipt_start_date = request.args.get('receipt_start_date', '')
    receipt_end_date = request.args.get('receipt_end_date', '')
    date_type = request.args.get('date_type', 'inspection_date')  # 默认为检验日期
    
    # 如果没有提供日期，默认设置为近7天
    if not start_date or not end_date:
        if not start_date:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
    
    material_number = request.args.get('material_number', '')
    supplier = request.args.get('supplier', '')
    quick_search = request.args.get('quick_search', '')
    purchase_order = request.args.get('purchase_order', '')
    inspection_result = request.args.get('inspection_result', '')
    
    # 不良率筛选
    defect_rate = request.args.get('defect_rate', '')
    defect_rate_op = request.args.get('defect_rate_op', 'gt')  # 默认为大于
    
    # 来料数量筛选
    total_quantity = request.args.get('total_quantity', '')
    total_quantity_op = request.args.get('total_quantity_op', 'gt')  # 默认为大于
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        # 根据日期类型应用日期筛选
        if date_type == 'inspection_date':
            # 检验日期筛选
            if start_date:
                conditions.append("inspection_date >= %s")
                params.append(start_date)
            
            if end_date:
                conditions.append("inspection_date <= %s")
                params.append(end_date)
        else:  # receipt_date
            # 来料日期筛选
            if start_date:
                conditions.append("receipt_date >= %s")
                params.append(start_date)
            
            if end_date:
                conditions.append("receipt_date <= %s")
                params.append(end_date)
        
        # 如果使用高级搜索中的来料日期
        if receipt_start_date:
            conditions.append("receipt_date >= %s")
            params.append(receipt_start_date)
        
        if receipt_end_date:
            conditions.append("receipt_date <= %s")
            params.append(receipt_end_date)
        
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        # 处理快速搜索
        if quick_search:
            conditions.append("(material_number LIKE %s OR supplier LIKE %s OR material_name LIKE %s OR purchase_order LIKE %s OR JSON_EXTRACT(defect_issues, '$[*].description') LIKE %s)")
            search_param = f"%{quick_search}%"
            params.extend([search_param, search_param, search_param, search_param, search_param])
        
        # 处理不良率筛选 - 抽样检验使用sample_quantity计算不良率
        if defect_rate:
            defect_rate_value = float(defect_rate)
            if defect_rate_op == 'gt':
                conditions.append("sample_quantity > 0 AND (defect_quantity / sample_quantity * 100) > %s")
            else:  # lte
                conditions.append("sample_quantity > 0 AND (defect_quantity / sample_quantity * 100) <= %s")
            params.append(defect_rate_value)
        
        # 处理来料数量筛选
        if total_quantity:
            total_quantity_value = int(total_quantity)
            if total_quantity_op == 'gt':
                conditions.append("total_quantity > %s")
            else:  # lte
                conditions.append("total_quantity <= %s")
            params.append(total_quantity_value)
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 计算总记录数
        count_query = f"SELECT COUNT(*) as total FROM sampling_inspection {where_clause}"
        cursor.execute(count_query, params)
        total_records = cursor.fetchone()['total']
        
        # 计算总页数
        total_pages = (total_records + per_page - 1) // per_page
        
        # 确保页码在有效范围内
        if page < 1:
            page = 1
        elif page > total_pages and total_pages > 0:
            page = total_pages
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        # 获取分页后的记录
        query = f"""
            SELECT * FROM sampling_inspection 
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, params + [per_page, offset])
        records = cursor.fetchall()
        
        # 获取所有供应商列表（用于筛选）
        cursor.execute("SELECT DISTINCT supplier FROM sampling_inspection ORDER BY supplier")
        suppliers = [row['supplier'] for row in cursor.fetchall()]
        
        # 渲染模板，传递分页和筛选信息
        return render_template(
            'sampling_inspection.html', 
            records=records,
            suppliers=suppliers,
            page=page,
            per_page=per_page,
            total_pages=total_pages,
            total_records=total_records,
            start_date=start_date,
            end_date=end_date,
            receipt_start_date=receipt_start_date,
            receipt_end_date=receipt_end_date,
            material_number=material_number,
            supplier=supplier,
            quick_search=quick_search,
            purchase_order=purchase_order,
            inspection_result=inspection_result,
            defect_rate=defect_rate,
            defect_rate_op=defect_rate_op,
            total_quantity=total_quantity,
            total_quantity_op=total_quantity_op,
            date_type=date_type
        )
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 新增抽样检验记录页面
@sampling_inspection_bp.route('/new')
def new_inspection():
    return render_template('new_sampling_inspection.html')

# 检验记录详情页面
@sampling_inspection_bp.route('/detail')
def detail_inspection():
    record_id = request.args.get('id')
    if not record_id:
        return "缺少记录ID", 400
    return render_template('detail_inspection.html', 
                          record_id=record_id, 
                          inspection_type='sampling') 