﻿# -*- coding: utf-8 -*-
"""
来料检验路由 - 批量导入检验功能
"""

from flask import render_template, request, jsonify
from . import incoming_inspection_bp
from db_config import get_db_connection
import os
import sys
from datetime import datetime

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))

# ------ 来料检验主蓝图路由 ------

# 来料检验首页
@incoming_inspection_bp.route('/')
def index():
    return render_template('index.html')

# 批量导入检验页面
@incoming_inspection_bp.route('/batch_import_inspection')
def batch_import_inspection_page():
    """批量导入检验页面"""
    return render_template('batch_import_inspection.html')

# 批量导入检验记录列表页面
@incoming_inspection_bp.route('/batch_inspection_records')
def batch_inspection_records_page():
    """批量导入检验记录列表页面"""
    return render_template('batch_inspection_records.html')

# 统一批量导入待检页面（保留现有功能）
@incoming_inspection_bp.route('/batch_import_sampling')
def batch_import_sampling():
    return render_template('batch_import_sampling.html')

# 批量导入待检 - 全部检验（重定向到统一页面）
@incoming_inspection_bp.route('/batch_import_full')
def batch_import_full():
    return render_template('batch_import_sampling.html')

# 待检清单
@incoming_inspection_bp.route('/pending_list')
def pending_list():
    inspection_type = request.args.get('type', 'sampling')
    return render_template('pending_list.html', inspection_type=inspection_type)

# 注意：所有检验记录相关的API已被删除
# 现在只保留批量导入检验功能和待检清单功能
