<!DOCTYPE html>
<html lang="zh-CN" style="margin: 0; padding: 0; width: 100%; overflow-x: hidden;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}品质中心管理系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 强制导航栏左对齐 */
        html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            overflow-x: hidden !important;
        }
        
        .navbar {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            left: 0 !important;
            right: 0 !important;
            position: relative !important;
        }
        
        .navbar .container {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            padding: 0 8px !important;
            margin-left: 0 !important; /* 从页面最左侧开始 */
            max-width: 100% !important; /* 使容器宽度为100% */
            width: 100% !important;
            margin: 0 !important;
        }
        
        .navbar-left {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            flex: 1 !important;
            margin-left: 0 !important;
            padding-left: 0 !important;
        }
        
        .navbar-brand {
            display: flex !important;
            align-items: center !important;
            margin-right: 10px !important;
            float: left !important;
            margin-left: 10px !important; /* 给LOGO添加左侧边距 */
        }
        
        .navbar-nav {
            display: flex !important;
            margin-right: 15px !important;
            float: left !important;
        }
        
        @media (max-width: 768px) {
            .navbar-left {
                display: flex !important;
                align-items: center !important;
                justify-content: flex-start !important;
                flex: 1 !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
            }
            
            .navbar-nav {
                display: flex !important;
                margin-top: 0 !important;
                margin-right: 10px !important;
                float: left !important;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body style="margin: 0; padding: 0; width: 100%; overflow-x: hidden;">
    <nav class="navbar" style="width: 100%; margin: 0; padding: 0; left: 0; right: 0;">
        <div class="container" style="margin: 0; padding: 0 8px; max-width: 100%; width: 100%;">
            <div class="navbar-left" style="display: flex; align-items: center; justify-content: flex-start; margin-left: 0; padding-left: 0;">
                <a href="/" class="navbar-brand" style="margin-left: 10px;">
                <div class="logo-img">Q</div>
            </a>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link">物料管理</a>
                    <div class="dropdown-menu">
                        <a href="{{ url_for('material_management.index') }}" class="dropdown-item">
                            <i class="fas fa-list"></i> 物料列表
                        </a>
                        <a href="{{ url_for('material_management.new_material') }}" class="dropdown-item">
                            <i class="fas fa-plus"></i> 新增物料
                        </a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link">来料检验</a>
                        <div class="dropdown-menu multi-column" style="will-change: transform; transform: translateZ(0);">
                            <!-- 添加标题头部 -->
                            <div class="dropdown-header">
                                <h3>来料检验</h3>
                            </div>
                            <div class="menu-grid">
                                <div class="menu-row">
                                    <div class="menu-cell">
                                        <a href="#" class="menu-item heading-2" data-panel="visualization-tab">
                                <i class="fas fa-chart-bar"></i> 可视化
                            </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="#" class="menu-item" data-panel="data-analysis-tab">
                                            数据分析
                                        </a>
                                    </div>
                                </div>
                                <div class="menu-row">
                                    <div class="menu-cell">
                                        <a href="#" class="menu-item heading-2" data-panel="sampling-inspection-tab">
                                <i class="fas fa-random"></i> 抽样检验
                            </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="{{ url_for('sampling_inspection.index') }}" class="menu-item">
                                            检验记录
                                        </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="menu-item">
                                            新增检验
                                        </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="{{ url_for('incoming_inspection.pending_list', type='sampling') }}" class="menu-item">
                                            批量导入待检
                                        </a>
                                    </div>
                                </div>

                        </div>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link">制程管控</a>
                        <div class="dropdown-menu multi-column" style="will-change: transform; transform: translateZ(0);">
                            <!-- 添加标题头部 -->
                            <div class="dropdown-header">
                                <h3>制程管控</h3>
                            </div>
                            <div class="menu-grid">
                                <div class="menu-row">
                                    <div class="menu-cell">
                                        <a href="#" class="menu-item heading-2" data-panel="workshop-abnormal-tab">
                                <i class="fas fa-exclamation-triangle"></i> 车间异常
                            </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="{{ url_for('process_control.workshop_abnormal') }}" class="menu-item">
                                            异常记录
                                        </a>
                                    </div>
                                    <div class="menu-cell">
                                        <a href="{{ url_for('process_control.new_abnormal') }}" class="menu-item">
                                            新增记录
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                </li>
            </ul>
            </div>
            <div class="navbar-right">
                <div class="multi-function-btn">
                    <a href="#" id="function-icon" class="nav-icon">
                        <i class="fas fa-bars"></i>
                    </a>
                </div>
                <div class="function-dropdown">
                    <a href="#" class="function-item" id="show-panel-btn">
                        <i class="fas fa-chart-bar"></i> 数据面板
                    </a>
                    <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="function-item">
                        <i class="fas fa-plus"></i> 新增检验
                    </a>
                    <a href="{{ url_for('sampling_inspection.index') }}" class="function-item">
                        <i class="fas fa-clipboard-list"></i> 检验记录
                    </a>
                    <a href="{{ url_for('system_settings.index') }}" class="function-item" id="settings-btn">
                        <i class="fas fa-cog"></i> 系统设置
                </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 浮动来料检验面板 -->
    <div id="floating-search-panel" class="floating-panel" style="will-change: transform; transform: translateZ(0);">
        <div class="panel-header">
            <h3>来料检验</h3>
            <span class="close-panel">&times;</span>
        </div>
        <div class="panel-body">
            <!-- 使用新的菜单表格布局代替原来的panel-tabs -->
            <div class="menu-grid">
                <div class="menu-row">
                    <div class="menu-cell">
                        <a href="#" class="menu-item heading-2" data-panel="visualization-tab">
                    <i class="fas fa-chart-bar"></i> 可视化
                        </a>
                    </div>
                    <div class="menu-cell">
                        <a href="#" class="menu-item" data-panel="data-analysis-tab">
                            数据分析
                        </a>
                    </div>
                </div>
                <div class="menu-row">
                    <div class="menu-cell">
                        <a href="#" class="menu-item heading-2" data-panel="sampling-inspection-tab">
                    <i class="fas fa-random"></i> 抽样检验
                        </a>
                    </div>
                    <div class="menu-cell">
                        <a href="{{ url_for('sampling_inspection.index') }}" class="menu-item">
                            检验记录
                        </a>
                    </div>
                    <div class="menu-cell">
                        <a href="{{ url_for('incoming_inspection.new_inspection') }}" class="menu-item">
                            新增检验
                        </a>
                    </div>
                    <div class="menu-cell">
                        <a href="{{ url_for('incoming_inspection.pending_list', type='sampling') }}" class="menu-item">
                            批量导入待检
                        </a>
                    </div>
                </div>

            </div>
            
            <div class="panel-content">
                <!-- 可视化内容 -->
                <div class="panel-tab-content active" id="visualization-tab">
                    <div class="float-card-container">
                        <div class="visualization-charts">
                            <div class="chart-card">
                                <h4>检验合格率趋势</h4>
                                <div class="chart-placeholder" id="trend-chart">图表加载中...</div>
                            </div>
                            <div class="chart-card">
                                <h4>供应商分布</h4>
                                <div class="chart-placeholder" id="supplier-chart">图表加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据分析内容 -->
                <div class="panel-tab-content" id="data-analysis-tab">
                    <div class="analytics-container">
                        <div class="summary-stats">
                            <div class="stat-card">
                                <h4>合格率趋势</h4>
                                <div class="chart-placeholder" id="rate-trend-chart">图表加载中...</div>
                            </div>
                            <div class="stat-card">
                                <h4>供应商分布</h4>
                                <div class="chart-placeholder" id="supplier-dist-chart">图表加载中...</div>
                            </div>
                            <div class="stat-card">
                                <h4>问题分类</h4>
                                <div class="chart-placeholder" id="issue-chart">图表加载中...</div>
                            </div>
                        </div>
                        <div class="data-list">
                            <h4>最近检验记录</h4>
                            <div id="recent-inspections-list">加载中...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 检验记录内容 -->
                <div class="panel-tab-content" id="inspection-records-tab">
                    <div class="inspection-records-container">
                        <div class="records-header">
                            <h4>最近检验记录</h4>
                            <div class="records-filter">
                                <select id="records-type-filter" class="form-control">
                                    <option value="all">全部检验</option>
                                    <option value="sampling">抽样检验</option>
                                    <option value="full">全检验</option>
                                </select>
                            </div>
                        </div>
                        <div class="records-list" id="inspection-records-list">
                            <div class="loading-indicator">加载中...</div>
                        </div>
                        <div class="records-pagination">
                            <button id="prev-records-page" class="btn btn-sm btn-primary" disabled>上一页</button>
                            <span id="records-page-info">第 1 页</span>
                            <button id="next-records-page" class="btn btn-sm btn-primary">下一页</button>
                        </div>
                    </div>
                </div>
                
                <!-- 抽样检验内容 -->
                <div class="panel-tab-content" id="sampling-inspection-tab">
                    <form id="floating-search-form">
                        <div class="form-section">
                            <div class="form-group horizontal">
                                <div class="form-label">物料料号</div>
                                <div class="form-input">
                                    <input type="text" id="float-material-number" class="form-control" placeholder="输入料号关键字">
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">物料名称</div>
                                <div class="form-input">
                                    <input type="text" id="float-material-name" class="form-control" placeholder="输入名称关键字">
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">供应商</div>
                                <div class="form-input">
                                    <input type="text" id="float-supplier" class="form-control" placeholder="输入供应商关键字">
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">采购单号</div>
                                <div class="form-input">
                                    <input type="text" id="float-purchase-order" class="form-control" placeholder="输入采购单号">
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">开始日期</div>
                                <div class="form-input">
                                    <input type="date" id="float-start-date" class="form-control">
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">结束日期</div>
                                <div class="form-input">
                                    <input type="date" id="float-end-date" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="form-group search-buttons">
                            <button type="submit" class="btn btn-success">搜索</button>
                            <button type="reset" class="btn btn-danger">重置</button>
                        </div>
                    </form>
                    <div class="search-results" id="search-results">
                        <!-- 搜索结果将显示在这里 -->
                    </div>
                </div>
                

                
                <!-- 新增检验内容 -->
                <div class="panel-tab-content" id="new-inspection-tab">
                    <div class="quick-form-container">
                        <h4>快速新增检验记录</h4>
                        <form id="quick-inspection-form">
                            <div class="form-group horizontal">
                                <div class="form-label">物料料号</div>
                                <div class="form-input">
                                    <input type="text" id="new-material-number" class="form-control" required>
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">检验类型</div>
                                <div class="form-input">
                                    <select id="new-inspection-type" class="form-control">
                                        <option value="sampling">抽样检验</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group horizontal">
                                <div class="form-label">供应商</div>
                                <div class="form-input">
                                    <input type="text" id="new-supplier" class="form-control" required>
                                </div>
                            </div>
                            <div class="form-buttons">
                                <button type="submit" class="btn btn-primary">创建检验</button>
                                <a href="{{ url_for('incoming_inspection.new_inspection') }}" class="btn btn-success">高级模式</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        {% block content %}{% endblock %}
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 