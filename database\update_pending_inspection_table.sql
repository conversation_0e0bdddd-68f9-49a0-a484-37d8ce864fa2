-- 更新待检物料表，添加免检支持
-- 执行前请备份数据库

-- 1. 修改inspection_type字段，添加exempt选项
ALTER TABLE pending_inspections 
MODIFY COLUMN inspection_type ENUM('sampling', 'full', 'exempt') NOT NULL 
COMMENT '检验类型：sampling-抽样检验，full-全部检验，exempt-免检';

-- 2. 修改批次表的inspection_type字段
ALTER TABLE pending_inspection_batches 
MODIFY COLUMN inspection_type ENUM('sampling', 'full', 'exempt') NOT NULL 
COMMENT '检验类型';

-- 3. 添加免检相关字段到待检物料表
ALTER TABLE pending_inspections 
ADD COLUMN exempt_reason VARCHAR(500) DEFAULT NULL COMMENT '免检原因',
ADD COLUMN exempt_approved_by VARCHAR(50) DEFAULT NULL COMMENT '免检审批人',
ADD COLUMN exempt_approved_at TIMESTAMP NULL DEFAULT NULL COMMENT '免检审批时间';

-- 4. 为免检字段添加索引
ALTER TABLE pending_inspections 
ADD INDEX idx_exempt_approved_by (exempt_approved_by),
ADD INDEX idx_exempt_approved_at (exempt_approved_at);

-- 5. 创建免检记录表（可选，用于更详细的免检记录管理）
CREATE TABLE IF NOT EXISTS exempt_inspection_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL COMMENT '物料料号',
    material_name VARCHAR(200) COMMENT '物料名称',
    specification VARCHAR(500) COMMENT '规格型号',
    supplier_name VARCHAR(200) COMMENT '供应商名称',
    purchase_order VARCHAR(100) COMMENT '采购单号',
    incoming_quantity DECIMAL(10,3) COMMENT '来料数量',
    unit VARCHAR(20) COMMENT '单位',
    batch_number VARCHAR(100) COMMENT '批次号',
    arrival_date DATE COMMENT '到货日期',
    exempt_reason VARCHAR(500) COMMENT '免检原因',
    exempt_type ENUM('supplier_qualified', 'material_stable', 'emergency', 'other') DEFAULT 'other' COMMENT '免检类型',
    approved_by VARCHAR(50) COMMENT '审批人',
    approved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
    report_code VARCHAR(50) UNIQUE COMMENT '免检报告编号',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_material_code (material_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_approved_by (approved_by),
    INDEX idx_approved_at (approved_at),
    INDEX idx_arrival_date (arrival_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='免检记录表';

-- 6. 更新现有数据（如果有的话）
-- 将现有的inspection_type为空或其他值的记录设置为sampling
UPDATE pending_inspections 
SET inspection_type = 'sampling' 
WHERE inspection_type NOT IN ('sampling', 'full', 'exempt');

UPDATE pending_inspection_batches 
SET inspection_type = 'sampling' 
WHERE inspection_type NOT IN ('sampling', 'full', 'exempt');

-- 7. 添加触发器，自动更新批次统计（可选）
DELIMITER $$

CREATE TRIGGER update_batch_stats_after_insert
AFTER INSERT ON pending_inspections
FOR EACH ROW
BEGIN
    UPDATE pending_inspection_batches 
    SET 
        total_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id
        ),
        pending_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'pending'
        ),
        in_progress_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'in_progress'
        ),
        completed_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'completed'
        )
    WHERE id = NEW.batch_id;
END$$

CREATE TRIGGER update_batch_stats_after_update
AFTER UPDATE ON pending_inspections
FOR EACH ROW
BEGIN
    UPDATE pending_inspection_batches 
    SET 
        pending_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'pending'
        ),
        in_progress_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'in_progress'
        ),
        completed_items = (
            SELECT COUNT(*) 
            FROM pending_inspections 
            WHERE batch_id = NEW.batch_id AND status = 'completed'
        )
    WHERE id = NEW.batch_id;
END$$

DELIMITER ;

-- 8. 验证更新结果
SELECT 
    COLUMN_NAME, 
    COLUMN_TYPE, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'pending_inspections' 
    AND COLUMN_NAME IN ('inspection_type', 'exempt_reason', 'exempt_approved_by', 'exempt_approved_at')
ORDER BY ORDINAL_POSITION;

-- 显示更新完成信息
SELECT '待检物料表结构更新完成！' AS message;
SELECT '已添加免检支持，包括：' AS info;
SELECT '1. inspection_type字段支持exempt选项' AS detail1;
SELECT '2. 添加了免检相关字段' AS detail2;
SELECT '3. 创建了免检记录表' AS detail3;
SELECT '4. 添加了批次统计触发器' AS detail4;
